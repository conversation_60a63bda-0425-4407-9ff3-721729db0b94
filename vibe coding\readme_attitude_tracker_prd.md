# Attitude Tracker in 3D - 产品需求文档（PRD）

## 1. 项目概述

**项目名称**：Attitude Tracker in 3D  
**项目类型**：基于前端的三维姿态可视化交互系统  
**用途**：**用于软件著作权申报材料的提交，满足功能性展示与代码体量要求**  
**开发方式**：纯前端实现，使用 HTML + JavaScript + Three.js + Vue 或 React  
**目标代码体量**：**注释去除后不少于 3000 行代码**

系统允许用户通过**输入角度与位置坐标**，在页面中实现姿态和位置的三维动态展示。用户可上传数据文件，或手动输入数值。系统支持保存动画轨迹、回放、截图导出等功能，具备完整功能结构与交互按钮，满足软著对“功能完整性”和“页面展示”的要求。

---

## 2. 功能模块说明

| 模块编号 | 模块名称 | 简要说明 |
|----------|----------|-----------|
| M1 | 首页模块 | 展示系统名称、说明文字、导航入口 |
| M2 | 数据输入模块 | 用户输入姿态角度（如 Pitch、Yaw、Roll）和位置坐标，或上传 CSV 文件批量导入 |
| M3 | 三维姿态可视化模块 | 使用 Three.js 展示骨架模型在空间中的姿态和位置变化 |
| M4 | 动画控制模块 | 提供播放、暂停、重置、回放控制，记录动画轨迹 |
| M5 | 模型与单位切换模块 | 用户可切换骨架类型、选择角度单位（角度制/弧度制） |
| M6 | 历史数据浏览模块 | 显示历史数据记录，支持快速回放与状态恢复 |
| M7 | 截图与导出模块 | 提供截图导出、轨迹保存、动画回放导出等功能 |

---

## 3. 用户交互流程

```
[首页页面]
    ↓ 点击“进入系统”
[输入数据页面]
    → 手动填写角度+位置 | 上传数据文件
    ↓ 点击“加载”
[三维展示页面]
    → 展示模型姿态变化
    → 可控制视角、缩放、播放、暂停、截图等
    → 可切换模型/单位
    ↓
[历史数据记录]
    → 点击记录可回放
```

---

## 4. 页面规划（建议截图页面）

| 页面编号 | 页面名称 | 功能内容 | 是否用于截图 |
|----------|-----------|-----------|----------------|
| P1 | 首页页面 | 展示系统LOGO/名称/按钮 | ✅ |
| P2 | 数据输入页面 | 表单输入与上传模块，含单位选择 | ✅ |
| P3 | 三维展示页面 | 骨架姿态展示，三维场景渲染 | ✅ |
| P4 | 控制按钮栏 | 播放、暂停、重置、截图、轨迹等按钮 | ✅ |
| P5 | 模型/单位切换面板 | 骨架模型下拉框、角度/弧度切换 | ✅ |
| P6 | 历史记录面板 | 展示历史轨迹记录，点击即可重播 | ✅ |

---

## 5. 技术架构

- **前端框架**：Vue 3 / React（用户选择其一）
- **可视化引擎**：Three.js
- **打包工具**：Vite
- **语言**：HTML5、CSS3、JavaScript (ES6)
- **模型格式支持**：glTF / JSON / 内置骨架

---

## 6. 开发环境配置（基于 VS Code）

### 推荐工具与插件：

- VS Code 插件：
  - Live Server（实时预览）
  - ESLint + Prettier（代码规范）
  - Path Intellisense（路径自动补全）
  - Three.js Snippets（快捷开发）
- 安装 Node.js（用于运行 Vite 开发环境）
- 创建项目结构：
  ```
  attitude-tracker-3d/
  ├─ index.html
  ├─ vite.config.js
  ├─ src/
  │  ├─ main.js
  │  ├─ App.vue / App.jsx
  │  ├─ components/
  │  ├─ pages/
  │  ├─ assets/
  │  ├─ models/
  │  └─ utils/
  └─ public/
  ```

---

## 7. 软件著作权合规说明

- 本系统设计了**6个以上功能页面**，可导出**不少于6张**具有交互按钮和结构层次的功能截图
- 源码采用模块化拆分，代码行数（去除注释）**目标超过3000行**
- 提供手动输入、数据上传、模型交互、轨迹记录、导出截图等完整功能链，满足著作权申请中“具备基本功能和操作界面”的要求
- 支持 Three.js 三维骨架动画展示，具备显著可视化特征

---

## 8. 后续可扩展方向（可选）

虽然本项目仅用于软著材料提交，但可设计良好的可扩展性，以备后续实际使用：

- 接入实时传感器数据流（如 IMU）
- 增加多骨架并行比较展示
- 添加用户权限管理与数据云存储
- 可导出 PDF 报告或演示视频

---

## 9. 页面视觉风格与排版设计（用于提升截图效果与用户体验）

### 9.1 外观设计目标

> 风格关键词：**现代感、科技感、清晰可截图、交互明显**

- **主色调**：科技蓝 + 中灰背景 或 深灰 + 荧光绿（突出技术风格）
- **字体建议**：`Inter`、`Roboto`、`Noto Sans`
- **UI 风格**：半透明卡片 + 圆角 + 阴影，避免生硬边框
- **按钮/交互组件**：Hover 效果明显，具备动画反馈
- **背景建议**：三维区域使用渐变、网格、或轻雾背景以增强模型视觉稳定感
- **动效处理**：加载动画、模型进入动画、过渡缓动曲线

### 9.2 页面排版建议（关键界面）

#### 数据输入页面（P2）

```
┌──────────────────────────────┐
│          顶部导航栏（系统名、logo）          │
├──────────────────────────────┤
│ 左侧侧边栏       │ 主操作区                      │
│ - 数据输入        │ ┌────────────────────┐ │
│ - 三维展示        │ │ 输入：角度、坐标            │ │
│ - 轨迹控制        │ │ 单位切换、文件上传          │ │
│ - 历史记录        │ └────────────────────┘ │
└──────────────────────────────┘
```

#### 三维姿态展示页面（P3）

```
┌──────────────────────────────┐
│ 顶部导航栏                          │
├──────────────────────────────┤
│   [Three.js模型展示区（占满屏宽）]  │
│ ┌────────────┐ ┌──────────┐  │
│ │ 模型区域     │ │ 控制面板按钮群   │  │
│ └────────────┘ └──────────┘  │
│ 底部控制栏：播放 | 暂停 | 截图 | 回放 | 重置视角     │
└──────────────────────────────┘
```

#### 历史记录页面（P6）

```
┌─────────── 历史轨迹卡片区域 ───────────┐
│  [轨迹1] [轨迹2] [轨迹3] ...                             │
│  点击可加载展示 → 进入回放视角                          │
└────────────────────────────────────┘
```

### 9.3 建议使用的视觉工具与库

| 工具 | 用途说明 |
|------|-----------|
| **Tailwind CSS** | 快速搭建现代布局、实现卡片/阴影/排版 |
| **shadcn/ui**（React） | 提供精美按钮、输入框、滑块等组件 |
| **Framer Motion** / `@vueuse/motion` | 页面切换、交互按钮动效 |
| **Lucide / Heroicons** | 图标系统，美化界面按钮功能性 |

---
