## 项目结构与总体设计

本项目为一个基于 Web 的三维姿态追踪系统（Attitude Tracker in 3D），使用 Three.js 进行骨架模型可视化，前端采用 React + TailwindCSS 实现。系统支持用户手动输入或上传包含角度与位置的数据文件，进行姿态展示、轨迹播放、截图导出等交互。

本设计为迭代1详细设计，覆盖所有核心模块的功能实现，并为后续功能扩展预留空实现结构。

---

## 目录结构树 (Directory Tree)

```
attitude-tracker-3d/
├── public/
│   └── index.html
├── src/
│   ├── App.jsx
│   ├── main.jsx
│   ├── assets/
│   ├── components/
│   │   ├── Header.jsx
│   │   ├── Sidebar.jsx
│   │   └── ControlPanel.jsx
│   ├── pages/
│   │   ├── Home.jsx
│   │   ├── DataInput.jsx
│   │   ├── Viewer3D.jsx
│   │   └── History.jsx
│   ├── models/
│   │   └── skeleton.glb
│   ├── services/
│   │   └── dataParser.js
│   ├── utils/
│   │   └── playback.js
│   └── store/
│       └── dataStore.js
├── vite.config.js
└── package.json
```

---

## 整体逻辑和交互时序图

```mermaid
sequenceDiagram
    participant App.jsx
    participant DataInput.jsx
    participant dataParser.js
    participant dataStore.js
    participant Viewer3D.jsx

    App.jsx->>DataInput.jsx: 渲染数据输入页
    DataInput.jsx->>dataParser.js: 解析用户输入/上传数据
    dataParser.js->>dataStore.js: 存储角度与坐标数组
    DataInput.jsx->>Viewer3D.jsx: 跳转展示页
    Viewer3D.jsx->>dataStore.js: 拉取数据
    Viewer3D.jsx-->>Viewer3D.jsx: 加载Three.js动画并播放
```

---

## 数据实体结构深化

### 数据结构定义

```ts
interface PosePoint {
  timestamp: number;
  pitch: number;
  yaw: number;
  roll: number;
  x: number;
  y: number;
  z: number;
}
```

### Mermaid 实体关系图

```mermaid
erDiagram
    PosePoint {
        number timestamp
        number pitch
        number yaw
        number roll
        number x
        number y
        number z
    }
```

---

## 配置项

系统基于 Vite + React，无需额外环境变量配置。模型资源位于 `/src/models/skeleton.glb`。

---

## 模块化文件详解 (File-by-File Breakdown)

### src/App.jsx

a. 用途说明：应用主框架，提供路由与页面切换结构。

b. 类图：无

c. 方法详解：仅为页面结构容器，无业务逻辑。

---

### src/pages/DataInput.jsx

a. 文件用途说明：用户输入姿态或上传CSV，触发数据解析

#### 函数详解：handleFileUpload(file)
- 用途: 接收用户上传文件并解析
- 输入参数: File 对象
- 输出: 无
```mermaid
flowchart TD
  A[用户上传CSV] --> B[handleFileUpload]
  B --> C[dataParser.js解析]
  C --> D[dataStore.js缓存数据]
  D --> E[跳转到Viewer3D]
```

---

### src/services/dataParser.js

a. 用途说明：解析CSV为PosePoint[]结构

#### 函数: parseCSV(fileContent)
- 输入: string
- 输出: PosePoint[]

---

### src/store/dataStore.js

a. 用途说明：全局状态管理模块

#### 函数: setData(poseList: PosePoint[])
- 用途: 存储姿态数据

#### 函数: getData()
- 输出: PosePoint[]

---

### src/pages/Viewer3D.jsx

a. 用途说明：核心Three.js视图容器

#### 函数: initScene()
- 用途: 初始化Three.js渲染器、相机、骨架

#### 函数: playAnimation()
- 用途: 读取store数据，播放模型动作

```mermaid
flowchart TD
  A[页面加载] --> B[initScene]
  B --> C[加载骨架模型]
  C --> D[拉取数据]
  D --> E[playAnimation: 帧驱动位姿更新]
```

---

## 迭代演进依据

1. 所有组件按“页+功能模块”划分，具备清晰演进边界
2. 除核心逻辑外，部分未来功能（如导出视频、多人对比）保留空实现结构
3. 数据结构统一采用PosePoint，避免未来结构变更影响多个模块
4. 每个功能页独立，便于后期插拔或嵌入至更大系统中
5. 预留状态管理与动画控制扩展接口，便于支持流数据、回放加速等后续功能

---

👉 本设计为迭代1详细设计，如你审核通过，将作为编码依据。请确认是否继续推进或需修改细节。

